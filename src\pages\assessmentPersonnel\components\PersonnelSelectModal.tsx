import React, { useState, useRef } from 'react';
import { Modal, Button, Space, message } from 'antd';
import { YTHList } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import systemApi from '@/service/system';
import type { User } from '@/service/system';
import type { ApiResponse } from '@/service/assessmentApi';

type PropsTypes = {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (selectedUsers: User[]) => void;
  selectedPersonnel: User[];
};

/**
 * @description 人员选择弹窗
 * @returns React.FC
 */
const PersonnelSelectModal: React.FC<PropsTypes> = ({
  visible,
  onCancel,
  onConfirm,
  selectedPersonnel = [],
}) => {
  const actionRef: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>(
    selectedPersonnel.map(person => person.id!)
  );
  const [selectedRows, setSelectedRows] = useState<User[]>(selectedPersonnel);

  /**
   * 确认选择
   */
  const handleConfirm: () => void = () => {
    if (selectedRows.length === 0) {
      message.warning('请至少选择一个人员');
      return;
    }
    onConfirm(selectedRows);
    onCancel();
  };

  /**
   * 取消选择
   */
  const handleCancel: () => void = () => {
    // 重置选择状态
    setSelectedRowKeys(selectedPersonnel.map(person => person.id!));
    setSelectedRows(selectedPersonnel);
    onCancel();
  };

  /**
   * 表格行选择配置
   */
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: User[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
      setSelectedRows(newSelectedRows);
    },
    getCheckboxProps: (record: User) => ({
      disabled: false,
      name: record.realName,
    }),
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'realName',
      title: '姓名',
      width: 120,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
      },
    },
    {
      dataIndex: 'userCode',
      title: '工号',
      width: 120,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入工号',
      },
    },
    {
      dataIndex: 'phone',
      title: '联系电话',
      width: 130,
      display: true,
    },
    {
      dataIndex: 'email',
      title: '邮箱',
      width: 180,
      display: true,
      ellipsis: true,
    },
    {
      dataIndex: 'unitId',
      title: '所属部门',
      width: 150,
      query: true,
      display: true,
      componentName: 'TreeSelector',
      componentProps: {
        request: async () => {
          const result = await systemApi.unitTree();
          return result ? [result] : [];
        },
        p_props: {
          placeholder: '请选择部门',
        },
      },
      render: (value: string, record: User) => {
        // 这里应该显示部门名称，暂时模拟
        return '模拟部门';
      },
    },
    {
      dataIndex: 'state',
      title: '状态',
      width: 100,
      display: true,
      render: (value: string) => {
        return value === '1' ? '在职' : '离职';
      },
    },
  ];

  return (
    <Modal
      title="选择考核人员"
      width="80%"
      open={visible}
      onCancel={handleCancel}
      maskClosable={false}
      footer={
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleConfirm}>
            确定选择 ({selectedRows.length})
          </Button>
        </Space>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <span style={{ color: '#1890ff' }}>
          已选择 {selectedRows.length} 个人员
        </span>
      </div>

      <YTHList
        defaultQuery={{}}
        code="personnelSelectList"
        action={listAction}
        searchMemory
        actionRef={actionRef}
        showRowSelection={true}
        rowSelection={rowSelection}
        operation={[]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          try {
            // 模拟API调用，实际应该调用真实的用户列表API
            const mockUsers: User[] = [
              {
                id: '1',
                realName: '张三',
                userCode: 'U001',
                phone: '13800138001',
                email: '<EMAIL>',
                state: '1',
                unitId: 'unit1',
              },
              {
                id: '2',
                realName: '李四',
                userCode: 'U002',
                phone: '13800138002',
                email: '<EMAIL>',
                state: '1',
                unitId: 'unit1',
              },
              {
                id: '3',
                realName: '王五',
                userCode: 'U003',
                phone: '13800138003',
                email: '<EMAIL>',
                state: '1',
                unitId: 'unit2',
              },
              {
                id: '4',
                realName: '赵六',
                userCode: 'U004',
                phone: '13800138004',
                email: '<EMAIL>',
                state: '1',
                unitId: 'unit2',
              },
              {
                id: '5',
                realName: '钱七',
                userCode: 'U005',
                phone: '13800138005',
                email: '<EMAIL>',
                state: '1',
                unitId: 'unit3',
              },
            ];

            // 简单的筛选逻辑
            let filteredUsers = mockUsers;
            if (filter.realName) {
              filteredUsers = filteredUsers.filter(user => 
                user.realName?.includes(filter.realName)
              );
            }
            if (filter.userCode) {
              filteredUsers = filteredUsers.filter(user => 
                user.userCode?.includes(filter.userCode)
              );
            }

            // 分页处理
            const startIndex = (pagination.current - 1) * pagination.pageSize;
            const endIndex = startIndex + pagination.pageSize;
            const pageData = filteredUsers.slice(startIndex, endIndex);

            // 添加序号
            pageData.forEach((item, index) => {
              (item as User & { serialNo?: number }).serialNo = startIndex + index + 1;
            });

            return {
              data: pageData,
              total: filteredUsers.length,
              success: true,
            };
          } catch (error) {
            console.error('获取人员列表失败:', error);
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        columns={columns}
      />
    </Modal>
  );
};

export default PersonnelSelectModal;
