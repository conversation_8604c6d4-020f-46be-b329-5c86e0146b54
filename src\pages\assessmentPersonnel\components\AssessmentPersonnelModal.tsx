import React, { useState, useEffect, useRef } from 'react';
import { YTHForm, YTHList } from 'yth-ui';
import { message, Button, Spin, Divider, Space } from 'antd';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import moment from 'moment';
import assessmentApi from '@/service/assessmentApi';
import systemApi from '@/service/system';
import type {
  ApiResponse,
  AssessmentPersonnel,
  AssessmentPersonnelDetail,
  AssessmentPersonnelSaveParams,
} from '@/service/assessmentApi';
import type { User } from '@/service/system';
import type { Form } from '@formily/core/esm/models';

type PropsTypes = {
  dataObj?: AssessmentPersonnel;
  closeModal: () => void;
  mode: 'add' | 'edit' | 'view';
};

/**
 * @description 考核人员安排 新增、编辑、查看弹窗
 * @returns React.FC
 */
const AssessmentPersonnelModal: React.FC<PropsTypes> = ({
  dataObj,
  closeModal = () => {},
  mode = 'add',
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  // YTHList 相关状态
  const listActionRef: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();
  const [selectedPersonnel, setSelectedPersonnel] = useState<User[]>([]);

  // 人员选择弹窗状态
  const [personnelModalVisible, setPersonnelModalVisible] = useState<boolean>(false);

  /**
   * 是否为只读模式
   */
  const isReadOnly: boolean = mode === 'view';

  /**
   * 人员列表列配置
   */
  const personnelColumns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'personnelName',
      title: '姓名',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'personnelCode',
      title: '工号',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'departmentName',
      title: '部门',
      width: 150,
      display: true,
    },
    {
      dataIndex: 'postName',
      title: '岗位',
      width: 120,
      display: true,
    },
    {
      dataIndex: 'phone',
      title: '联系电话',
      width: 130,
      display: true,
    },
    {
      dataIndex: 'arrangeStatusText',
      title: '状态',
      width: 100,
      display: true,
      render: (value: string) => value || '已安排',
    },
    {
      dataIndex: 'arrangeTime',
      title: '安排时间',
      width: 160,
      display: true,
      render: (value: string) => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      dataIndex: 'remark',
      title: '备注',
      width: 150,
      display: true,
      ellipsis: true,
    },
  ];

  useEffect(() => {
    if (dataObj && dataObj.id) {
      // 编辑或查看模式，设置表单值
      form.setValues({
        ...dataObj,
        assessmentTime: [dataObj.startTime, dataObj.endTime],
      });
    } else {
      // 新增模式，设置默认值
      form.setValues({
        assessmentStatus: '1', // 默认待开始
        assessmentTime: [
          moment().format('YYYY-MM-DD HH:mm:ss'),
          moment().add(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        ],
      });
    }
  }, [dataObj, form]);

  /**
   * 取消操作
   */
  const handleCancel: () => void = () => {
    form.reset();
    closeModal();
  };

  /**
   * 保存操作
   */
  const handleSave: () => Promise<void> = async () => {
    try {
      setIsSaving(true);

      // 表单验证
      await form.validate();
      const formValues = form.getValues();

      // 构造保存参数
      const saveParams: AssessmentPersonnelSaveParams = {
        id: dataObj?.id,
        assessmentName: formValues.assessmentName,
        assessmentType: formValues.assessmentType,
        startTime: formValues.assessmentTime[0],
        endTime: formValues.assessmentTime[1],
        description: formValues.description,
        unitId: formValues.unitId,
        personnelList: selectedPersonnel.map((person: User) => ({
          personnelId: person.id!,
          personnelName: person.realName!,
          remark: '',
        })),
      };

      const result: ApiResponse<boolean> = await assessmentApi.saveAssessmentPersonnel(saveParams);

      if (result.code === 200) {
        message.success(mode === 'add' ? '新增成功' : '编辑成功');
        closeModal();
      } else {
        message.error(result.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败，请检查输入信息');
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * 添加人员
   */
  const handleAddPersonnel: () => void = () => {
    setPersonnelModalVisible(true);
  };

  /**
   * 删除人员
   */
  const handleDeletePersonnel: (personnelId: string) => void = (personnelId: string) => {
    setSelectedPersonnel((prev) => prev.filter((person) => person.id !== personnelId));
  };

  return (
    <div>
      <Spin spinning={isLoading}>
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="assessmentName"
            title="考核名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入考核名称',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="assessmentType"
            title="考核类型"
            labelType={1}
            required
            componentName="Selector"
            componentProps={{
              request: async () => {
                return [
                  { code: '1', text: '技能考核' },
                  { code: '2', text: '安全考核' },
                  { code: '3', text: '综合考核' },
                  { code: '4', text: '专项考核' },
                ];
              },
              p_props: {
                placeholder: '请选择考核类型',
                disabled: isReadOnly,
              },
            }}
          />

          <YTHForm.Item
            name="unitId"
            title="组织机构"
            labelType={1}
            required
            componentName="TreeSelector"
            componentProps={{
              request: async () => {
                const result = await systemApi.unitTree();
                return result ? [result] : [];
              },
              p_props: {
                placeholder: '请选择组织机构',
                disabled: isReadOnly,
              },
            }}
          />

          <YTHForm.Item
            name="assessmentTime"
            title="考核时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
              placeholder: ['开始时间', '结束时间'],
              disabled: isReadOnly,
              mode: 'range',
            }}
          />

          <YTHForm.Item
            name="description"
            title="考核描述"
            labelType={1}
            mergeRow={1}
            componentName="TextArea"
            componentProps={{
              placeholder: '请输入考核描述',
              disabled: isReadOnly,
              rows: 3,
            }}
          />
        </YTHForm>

        <Divider orientation="left">考核人员安排</Divider>

        {!isReadOnly && (
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddPersonnel}>
              添加人员
            </Button>
          </div>
        )}

        {/* 人员列表 */}
        <YTHList
          defaultQuery={{}}
          code="assessmentPersonnelDetailList"
          action={listAction}
          actionRef={listActionRef}
          showRowSelection={false}
          operation={[]}
          listKey="id"
          extraOperation={[]}
          request={async (filter, pagination) => {
            if (mode === 'add') {
              // 新增模式，显示已选择的人员
              const mockData: AssessmentPersonnelDetail[] = selectedPersonnel.map(
                (person, index) => ({
                  id: person.id!,
                  assessmentId: '',
                  personnelId: person.id!,
                  personnelName: person.realName!,
                  personnelCode: person.userCode!,
                  departmentName: '模拟部门',
                  postName: '模拟岗位',
                  phone: person.phone,
                  email: person.email,
                  arrangeStatus: '1',
                  arrangeStatusText: '已安排',
                  arrangeTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                  remark: '',
                  serialNo: index + 1,
                }),
              );

              return {
                data: mockData,
                total: mockData.length,
                success: true,
              };
            } else if (dataObj?.id) {
              // 编辑或查看模式，从API获取数据
              const resData: ApiResponse<AssessmentPersonnelDetail[]> =
                await assessmentApi.queryAssessmentPersonnelDetailList({
                  aescs: [],
                  descs: [],
                  condition: { assessmentId: dataObj.id },
                  currentPage: pagination.current,
                  pageSize: pagination.pageSize,
                });

              if (resData.code === 200) {
                resData.data.forEach((item, index) => {
                  resData.data[index].serialNo =
                    (pagination.current - 1) * pagination.pageSize + index + 1;
                });
                return {
                  data: resData.data,
                  total: resData.total,
                  success: true,
                };
              }
            }

            return {
              data: [],
              total: 0,
              success: false,
            };
          }}
          rowOperationWidth={isReadOnly ? 0 : 80}
          rowOperation={
            isReadOnly
              ? undefined
              : (row: AssessmentPersonnelDetail) => {
                  return [
                    {
                      element: (
                        <Button
                          type="link"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => handleDeletePersonnel(row.personnelId)}
                        >
                          删除
                        </Button>
                      ),
                    },
                  ];
                }
          }
          columns={personnelColumns}
        />

        {/* 底部按钮 */}
        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>{isReadOnly ? '关闭' : '取消'}</Button>
            {!isReadOnly && (
              <Button type="primary" loading={isSaving} onClick={handleSave}>
                保存
              </Button>
            )}
          </Space>
        </div>
      </Spin>

      {/* 人员选择弹窗 - 这里可以后续扩展为独立组件 */}
      {personnelModalVisible && <div>{/* 人员选择逻辑，暂时简化处理 */}</div>}
    </div>
  );
};

export default AssessmentPersonnelModal;
